#include <iostream>
#include <vector>
#include <climits>
#include <algorithm>
#include <iomanip>

using namespace std;

const int N = 5; // 假设图中有5个顶点
const int INF = 999; // 用999表示两个顶点之间没有直接的路径

void printMatrix(int c[][N], int n) {
    cout << "每两个顶点之间的权值，即原始数据，从顶点1，1开始" << endl;
    cout << "有向图权的邻接矩阵:" << endl;
    
    for (int i = 0; i < n; i++) {
        for (int j = 0; j < n; j++) {
            if (c[i][j] == 0 && i != j) {
                cout << setw(6) << "无";
            } else if (c[i][j] == 0 && i == j) {
                cout << setw(6) << "无";
            } else {
                cout << setw(6) << c[i][j];
            }
        }
        cout << endl;
    }
    cout << endl;
}

void Dijkstra(int n, int v, int c[][N]) {
    bool s[N];
    int dist[N];
    int prev[N];
    int i, j, minDist, minIndex;

    // 初始化 dist 和 prev 数组
    for (i = 0; i < n; i++) {
        if (i == v) {
            dist[i] = 0;
        } else if (c[v][i] != 0) {
            dist[i] = c[v][i];
        } else {
            dist[i] = INF;
        }
        s[i] = false;
        prev[i] = (c[v][i] != 0 && i != v) ? v : -1;
    }

    s[v] = true;

    for (i = 1; i < n; i++) { // 做n-1次贪心选择
        minDist = INF;
        minIndex = -1;

        // 取出V-S中具有最短特殊路径长度的顶点u
        for (j = 0; j < n; j++) {
            if (!s[j] && dist[j] < minDist) {
                minDist = dist[j];
                minIndex = j;
            }
        }

        if (minIndex == -1) break; // 没有可达节点
        s[minIndex] = true;

        // 根据作出的贪心选择更新Dist值
        for (j = 0; j < n; j++) {
            if (!s[j] && c[minIndex][j] != 0 && (minDist + c[minIndex][j] < dist[j])) {
                dist[j] = minDist + c[minIndex][j];
                prev[j] = minIndex;
            }
        }
    }

    // 输出结果
    cout << "选择源点为：" << v + 1 << endl;
    for (i = 0; i < n; i++) {
        if (i != v) {
            cout << "源点" << v + 1 << "到点" << i + 1 << "的最短路径长度为：" << dist[i] << "，其路径为：";
            vector<int> path;
            for (int at = i; at != -1; at = prev[at]) {
                path.push_back(at);
            }
            reverse(path.begin(), path.end());
            for (size_t k = 0; k < path.size(); k++) {
                cout << path[k] + 1;
                if (k < path.size() - 1) cout << "->";
            }
            cout << endl;
        }
    }
    cout << "请按任意键继续..." << endl;
}

int main() {
    int c[][N] = {
        {0, 10, 0, 30, 100},
        {0, 0, 50, 0, 0},
        {0, 0, 0, 10, 60},
        {0, 0, 20, 0, 0},
        {0, 0, 0, 0, 0}
    };

    int n = 5; // 图中有5个顶点
    int v = 0; // 源点为1（数组下标从0开始）

    printMatrix(c, n);
    Dijkstra(n, v, c);

    return 0;
}
