<template>
  <div class="manufacturer-detail">
    <RtHeader :title="manufacturer?.manufacturerName || '生产商详情'" />
    
    <div v-if="loading" class="loading">
      <el-skeleton :rows="5" animated />
    </div>
    
    <div v-else-if="manufacturer" class="container">
      <!-- 生产商基本信息 -->
      <div class="manufacturer-header">
        <div class="manufacturer-logo">
          <img
            :src="manufacturer.logoUrl || defaultLogo"
            :alt="manufacturer.manufacturerName"
            @error="handleImageError"
          />
        </div>
        
        <div class="manufacturer-info">
          <h1 class="manufacturer-name">{{ manufacturer.manufacturerName }}</h1>
          <p class="manufacturer-desc">{{ manufacturer.description || '暂无描述' }}</p>
          
          <div class="manufacturer-contact">
            <div v-if="manufacturer.contactInfo" class="contact-item">
              <el-icon><Phone /></el-icon>
              <span>{{ manufacturer.contactInfo }}</span>
            </div>
            <div v-if="manufacturer.address" class="contact-item">
              <el-icon><Location /></el-icon>
              <span>{{ manufacturer.address }}</span>
            </div>
            <div v-if="manufacturer.website" class="contact-item">
              <el-icon><Link /></el-icon>
              <a :href="manufacturer.website" target="_blank">{{ manufacturer.website }}</a>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 商品列表 -->
      <div class="products-section">
        <h2 class="section-title">
          <el-icon><ShoppingBag /></el-icon>
          商品列表
        </h2>
        
        <div v-if="productsLoading" class="products-loading">
          <el-skeleton :rows="3" animated />
        </div>
        
        <div v-else-if="products.length > 0" class="products-grid">
          <div 
            v-for="product in products" 
            :key="product.productId"
            class="product-card"
            @click="goToProductDetail(product.productId)"
          >
            <div class="product-image">
              <img 
                :src="product.imagePath || '/src/assets/images/default-product.png'" 
                :alt="product.productName"
                @error="handleProductImageError"
              />
            </div>
            
            <div class="product-info">
              <h3 class="product-name">{{ product.productName }}</h3>
              <p class="product-desc">{{ product.description || '暂无描述' }}</p>
              <div class="product-price">
                <span class="price">¥{{ product.price }}</span>
                <span class="stock">库存: {{ product.stockQuantity }}</span>
              </div>
            </div>
          </div>
        </div>
        
        <div v-else class="empty-products">
          <el-empty description="该生产商暂无商品" />
        </div>
      </div>
    </div>
    
    <div v-else class="error-state">
      <el-result
        icon="error"
        title="生产商不存在"
        sub-title="请检查链接是否正确"
      >
        <template #extra>
          <el-button type="primary" @click="goBack">返回</el-button>
        </template>
      </el-result>
    </div>
    
    <RtTabBar />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Phone, Location, Link, ShoppingBag } from '@element-plus/icons-vue'
import { manufacturerDetailService } from '@/api/manufacturer'
import { productListByManufacturerService } from '@/api/product'
import defaultLogo from '@/assets/images/default.png'

const route = useRoute()
const router = useRouter()

const manufacturer = ref(null)
const products = ref([])
const loading = ref(false)
const productsLoading = ref(false)

// 获取生产商详情
const getManufacturerDetail = async () => {
  try {
    loading.value = true
    const manufacturerId = route.params.id
    const result = await manufacturerDetailService(manufacturerId)
    
    if (result && result.code === 0) {
      manufacturer.value = result.data
      // 获取该生产商的商品列表
      await getManufacturerProducts(manufacturerId)
    } else {
      ElMessage.error(result?.message || '获取生产商详情失败')
    }
  } catch (error) {
    console.error('获取生产商详情失败:', error)
    ElMessage.error('获取生产商详情失败')
  } finally {
    loading.value = false
  }
}

// 获取生产商商品列表
const getManufacturerProducts = async (manufacturerId) => {
  try {
    productsLoading.value = true
    const result = await productListByManufacturerService(manufacturerId)
    
    if (result && result.code === 0) {
      products.value = result.data || []
    } else {
      console.error('获取商品列表失败:', result?.message)
    }
  } catch (error) {
    console.error('获取商品列表失败:', error)
  } finally {
    productsLoading.value = false
  }
}

// 跳转到商品详情
const goToProductDetail = (productId) => {
  router.push(`/product/${productId}`)
}

// 返回上一页
const goBack = () => {
  router.go(-1)
}

// 处理图片加载错误
const handleImageError = (event) => {
  event.target.src = defaultLogo
}

const handleProductImageError = (event) => {
  event.target.src = defaultLogo
}

onMounted(() => {
  getManufacturerDetail()
})
</script>

<style scoped>
.manufacturer-detail {
  min-height: 100vh;
  background: #f6f8fa;
}

.container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  margin-bottom: 80px;
}

.loading, .products-loading {
  padding: 20px;
}

.manufacturer-header {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 24px;
  align-items: flex-start;
}

.manufacturer-logo img {
  width: 120px;
  height: 120px;
  object-fit: contain;
  border-radius: 12px;
  border: 1px solid #e4e7ed;
  padding: 12px;
}

.manufacturer-info {
  flex: 1;
}

.manufacturer-name {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 12px 0;
}

.manufacturer-desc {
  font-size: 16px;
  color: #606266;
  line-height: 1.6;
  margin: 0 0 20px 0;
}

.manufacturer-contact {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #606266;
}

.contact-item a {
  color: #409EFF;
  text-decoration: none;
}

.contact-item a:hover {
  text-decoration: underline;
}

.products-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 20px 0;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

.product-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.product-card:hover {
  border-color: #409EFF;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.product-image {
  text-align: center;
  margin-bottom: 12px;
}

.product-image img {
  width: 100px;
  height: 100px;
  object-fit: contain;
  border-radius: 6px;
}

.product-name {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin: 0 0 8px 0;
}

.product-desc {
  font-size: 14px;
  color: #909399;
  margin: 0 0 12px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-price {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price {
  font-size: 18px;
  font-weight: 600;
  color: #e6a23c;
}

.stock {
  font-size: 12px;
  color: #909399;
}

.empty-products {
  text-align: center;
  padding: 40px 20px;
}

.error-state {
  padding: 40px 20px;
}

@media (max-width: 768px) {
  .container {
    padding: 16px;
  }
  
  .manufacturer-header {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }
  
  .manufacturer-logo img {
    width: 100px;
    height: 100px;
  }
  
  .manufacturer-name {
    font-size: 24px;
  }
  
  .products-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}
</style>
