<template>
  <div class="user-role-badge">
    <el-tag 
      :type="roleConfig.type" 
      :effect="effect"
      :size="size"
      :icon="roleConfig.icon"
    >
      {{ roleConfig.label }}
    </el-tag>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { User, Shop, Setting } from '@element-plus/icons-vue'

const props = defineProps({
  role: {
    type: String,
    default: 'CUSTOMER'
  },
  size: {
    type: String,
    default: 'default'
  },
  effect: {
    type: String,
    default: 'light'
  }
})

// 角色配置
const roleConfig = computed(() => {
  const configs = {
    CUSTOMER: {
      label: '普通用户',
      type: 'info',
      icon: User
    },
    MANUFACTURER: {
      label: '生产商',
      type: 'warning',
      icon: Shop
    },
    ADMIN: {
      label: '管理员',
      type: 'danger',
      icon: Setting
    }
  }
  
  return configs[props.role] || configs.CUSTOMER
})
</script>

<style scoped>
.user-role-badge {
  display: inline-block;
}
</style>
