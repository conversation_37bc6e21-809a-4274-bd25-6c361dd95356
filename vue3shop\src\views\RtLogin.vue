<script setup>
import { User, Lock } from '@element-plus/icons-vue'
import { ref } from 'vue'
import { useUserStore } from '@/stores/user'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 控制注册与登录表单的显示， 默认显示登录(因为我们登录和注册用的同一个vue文件)
const isRegister = ref(false)
const form = ref(null)  // 添加表单引用

//定义数据模型
const registerData = ref({
  username:'',
  password:'',
  rePassword:'',
  role: 'CUSTOMER'  // 默认为普通用户
})

const loginData = ref({
  username: '',
  password: ''
})

// 角色选项
const roleOptions = [
  { label: '普通用户', value: 'CUSTOMER' },
  { label: '生产商', value: 'MANUFACTURER' },
  { label: '管理员', value: 'ADMIN' }
]

//定义密码校验规则
const checkPassword=(rule,value,callback)=>{
  if(value==''){
    callback(new Error('请再次确认密码'))
  }else if(value!==registerData.value.password){
    callback(new Error('两次密码不一致'))
  }else{
    callback()
  }
}

//定义表单校验规则
const rules={
  username:[
    {required:true,message:'请输入用户名',trigger:'blur'},
    {min:5,max:16,message:'长度为5-16位非空字符',trigger:'blur'}
  ],
  password:[
    {required:true,message:'请输入密码',trigger:'blur'},
    {min:5,max:16,message:'长度为5-16位非空字符',trigger:'blur'}
  ],
  rePassword:[
    {validator:checkPassword,trigger:'blur'}
  ]
}

//调用后台接口，完成注册
const register = async () => {
  try {
    // 表单验证
    await form.value.validate()
    
    // 检查两次密码是否一致
    if (registerData.value.password !== registerData.value.rePassword) {
      ElMessage.error('两次密码不一致')
      return
    }
    
    // 调用注册接口
    const success = await userStore.register(registerData.value)
    
    if (success) {
      // 注册成功后切换到登录表单
      isRegister.value = false
      // 清空注册表单
      registerData.value = {
        username: '',
        password: '',
        rePassword: ''
      }
    }
  } catch (error) {
    console.error('注册失败:', error)
  }
}

// 登录方法
const login = async () => {
  try {
    // 表单验证
    await form.value.validate()
    
    // 调用登录接口
    const success = await userStore.login(loginData.value)
    
    if (success) {
      // 获取重定向地址，如果没有则跳转到首页
      const redirectPath = route.query.redirect || '/'
      router.push(redirectPath)
    }
  } catch (error) {
    console.error('登录失败:', error)
  }
}

// 重置表单数据
const resetForm = () => {
  if (isRegister.value) {
    registerData.value = {
      username: '',
      password: '',
      rePassword: '',
      role: 'CUSTOMER'
    }
  } else {
    loginData.value = {
      username: '',
      password: ''
    }
  }
  // 重置表单验证
  if (form.value) {
    form.value.resetFields()
  }
}

// 切换表单时重置数据
const toggleForm = (value) => {
  isRegister.value = value
  resetForm()
}
</script>

<template>
  <div class="login-page">
    <div class="login-image-section"></div>
    <div class="login-form-section">
      <div class="login-container">
        <!-- 注册表单 -->
        <el-form ref="form" size="large" autocomplete="off" v-if="isRegister" class="login-form" :model="registerData" :rules="rules">
          <el-form-item>
            <h1 class="form-title">注册</h1>
          </el-form-item>
          <el-form-item prop="username">
            <el-input :prefix-icon="User" placeholder="请输入用户名" v-model="registerData.username"></el-input>
          </el-form-item>
          <el-form-item prop="password">
            <el-input :prefix-icon="Lock" type="password" placeholder="请输入密码" v-model="registerData.password"></el-input>
          </el-form-item>
          <el-form-item prop="rePassword">
            <el-input :prefix-icon="Lock" type="password" placeholder="请再次输入密码" v-model="registerData.rePassword"></el-input>
          </el-form-item>
          <!-- 角色选择 -->
          <el-form-item>
            <el-select v-model="registerData.role" placeholder="请选择用户类型" style="width: 100%">
              <el-option
                v-for="option in roleOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
                :disabled="option.value === 'ADMIN'"
              />
            </el-select>
          </el-form-item>
          <!-- 注册按钮 -->
          <el-form-item>
            <el-button class="submit-button" type="primary" auto-insert-space @click="register" :loading="userStore.loading">
              注册
            </el-button>
          </el-form-item>
          <el-form-item class="form-footer">
            <el-link type="info" underline="never" @click="toggleForm(false)">
              ← 返回登录
            </el-link>
          </el-form-item>
        </el-form>
        <!-- 登录表单 -->
        <el-form ref="form" size="large" autocomplete="off" v-else class="login-form" :model="loginData" :rules="rules">
          <el-form-item>
            <h1 class="form-title">登录</h1>
          </el-form-item>
          <el-form-item prop="username">
            <el-input :prefix-icon="User" placeholder="请输入用户名" v-model="loginData.username"></el-input>
          </el-form-item>
          <el-form-item prop="password">
            <el-input name="password" :prefix-icon="Lock" type="password" placeholder="请输入密码" v-model="loginData.password"></el-input>
          </el-form-item>
          <!-- 登录提示 -->
          <el-form-item>
            <div class="login-tips">
              <el-text type="info" size="small">
                系统将根据您的账户自动识别身份类型
              </el-text>
            </div>
          </el-form-item>
          <el-form-item class="form-options">
            <div class="options-wrapper">
              <el-checkbox>记住我</el-checkbox>
              <el-link type="primary" underline="never">忘记密码？</el-link>
            </div>
          </el-form-item>
          <!-- 登录按钮 -->
          <el-form-item>
            <el-button class="submit-button" type="primary" auto-insert-space @click="login" :loading="userStore.loading">登录</el-button>
          </el-form-item>
          <el-form-item class="form-footer">
            <el-link type="info" underline="never" @click="toggleForm(true)">
              注册账号 →
            </el-link>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.login-page {
  min-height: 100vh;
  display: flex;
  // 移除背景渐变，由左右分栏背景负责
  // background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);
  
  .login-image-section {
    flex: 1.2; // 图片区域稍大
    background: url('@/assets/images/background.png') no-repeat center center;
    background-size: cover;
    height: 100vh;
  }

  .login-form-section {
    flex: 1; // 表单区域
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background-color: #f8fafd; // 给表单区域一个浅色背景
    padding: 20px;
  }

  .login-container {
    width: 100%;
    max-width: 400px; // 调整最大宽度
    padding: 30px; // 调整内边距
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1); // 调整阴影
  }

  .login-form {
    .form-title {
      font-size: 26px; // 调整标题字体大小
      color: #333;
      text-align: center;
      margin-bottom: 25px; // 调整底部外边距
      font-weight: 600;
    }

    .el-form-item {
      margin-bottom: 20px; // 调整表单项底部外边距
    }

    .el-input {
      --el-input-height: 45px; // 调整输入框高度
      
      :deep(.el-input__wrapper) {
        box-shadow: 0 0 0 1px #dcdfe6 inset;
        transition: box-shadow 0.2s ease-in-out;
        
        &:hover {
          box-shadow: 0 0 0 1px #c0c4cc inset;
        }
        
        &.is-focus {
          box-shadow: 0 0 0 1px var(--el-color-primary) inset;
        }
      }
    }

    .submit-button {
      width: 100%;
      height: 45px; // 与输入框高度一致
      font-size: 16px;
      margin-top: 15px; // 调整顶部外边距
      letter-spacing: 1px;
    }

    .form-options {
      margin-bottom: 15px; // 调整底部外边距
      
      .options-wrapper {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
      }
    }

    .form-footer {
      text-align: center;
      margin-top: 20px; // 调整顶部外边距

      .el-link {
        font-size: 14px;
        color: #606266;
        transition: color 0.2s ease-in-out;

        &:hover {
          color: var(--el-color-primary);
        }
      }
    }

    .login-tips {
      text-align: center;
      padding: 8px 0;
    }
  }
}

// 响应式适配
@media screen and (max-width: 992px) { // 调整断点，在中等屏幕下也显示左右分栏，但比例不同
  .login-page {
    .login-image-section {
      flex: 1; // 图片和表单区域比例均等
    }
    .login-form-section {
      flex: 1;
    }
  }
}

@media screen and (max-width: 768px) {
  .login-page {
    flex-direction: column;
    .login-image-section {
      display: none;
    }
    .login-form-section {
      flex: none;
      width: 100%;
      min-height: auto;
      padding: 20px 0; // 移除左右内边距，让 container 负责内边距
      background-color: #fff; // 小屏幕下表单背景与页面背景一致
    }
    .login-container {
       max-width: 360px; // 调整最大宽度
       margin: 20px auto; // 居中并添加上下外边距
       padding: 25px; // 调整内边距
       box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08); // 调整阴影
    }
     .login-form {
       .form-title {
         font-size: 24px;
         margin-bottom: 20px;
       }
       .el-form-item {
         margin-bottom: 18px;
       }
       .el-input {
         --el-input-height: 40px;
       }
       .submit-button {
         height: 40px;
         font-size: 15px;
         margin-top: 10px;
       }
       .form-options {
         margin-bottom: 10px;
       }
       .form-footer {
         margin-top: 15px;
       }
     }
  }
}

@media screen and (max-width: 480px) {
  .login-page {
    .login-form-section {
      padding: 10px 0; // 进一步减小内边距
    }
    .login-container {
      max-width: 300px; // 进一步调整最大宽度
      margin: 15px auto; // 调整外边距
      padding: 20px; // 进一步调整内边距
    }
     .login-form {
       .form-title {
         font-size: 22px;
       }
       .el-form-item {
         margin-bottom: 15px;
       }
       .submit-button {
         font-size: 14px;
       }
       .form-options {
         font-size: 13px; // 调整选项字体大小
       }
       .form-footer {
         .el-link {
           font-size: 13px; // 调整链接字体大小
         }
       }
     }
  }
}
</style>