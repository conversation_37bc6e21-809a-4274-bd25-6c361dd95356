import { defineStore } from 'pinia'
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { productListService, productDetailService, productAddService, productUpdateService, productDeleteService } from '@/api/product'
import { useUserStore } from '@/stores/user'

const defaultImageUrl = 'https://via.placeholder.com/300x200?text=No+Image'

export const useProductStore = defineStore('product', () => {
  const products = ref([])

  // 获取商品列表
  const getProductList = async () => {
    try {
      const response = await productListService()
      console.log('获取商品列表响应:', response)
      
      if (response.code === 0) {
        // 修改字段名称以匹配后端返回的数据
        products.value = response.data.map(product => ({
          id: product.productId,
          name: product.productName,
          description: product.description,
          categoryId: product.categoryId, // 添加分类ID
          stockQuantity: product.stockQuantity,
          price: product.price,
          imgUrl: product.imagePath || defaultImageUrl // 处理可能的空值
        }))
        return true
      } else {
        ElMessage.error(response.message || '获取商品列表失败')
        return false
      }
    } catch (error) {
      console.error('获取商品列表错误:', error)
      ElMessage.error('获取商品列表失败')
      return false
    }
  }

  // 获取商品详情
  const getProductDetail = async (productId) => {
    try {
      const response = await productDetailService(productId)
      console.log('获取商品详情响应:', response)
      
      if (response.code === 0) {
        // 修改字段名称以匹配后端返回的数据
        const imagePath = response.data.imagePath || defaultImageUrl
        return {
          id: response.data.productId,
          name: response.data.productName,
          stockQuantity: response.data.stockQuantity,
          price: response.data.price,
          description: response.data.description,
          images: [imagePath] // 使用有效的图片URL
        }
      } else {
        ElMessage.error(response.message || '获取商品详情失败')
        return null
      }
    } catch (error) {
      console.error('获取商品详情错误:', error)
      ElMessage.error('获取商品详情失败')
      return null
    }
  }

  // 添加商品
  const addProduct = async (productData) => {
    const userStore = useUserStore()
    if (!userStore.token) {
      ElMessage.error('请先登录')
      return false
    }

    try {
      // 确保数据格式正确
      const formattedData = {
        ...productData,
        imagePath: productData.imgUrl || productData.imagePath // 确保imagePath字段存在
      };
      
      console.log('添加商品数据:', formattedData);
      
      const response = await productAddService(formattedData)
      if (response.code === 0) {
        ElMessage.success('添加成功')
        await getProductList() // 刷新商品列表
        return true
      } else {
        ElMessage.error(response.message || '添加失败')
        return false
      }
    } catch (error) {
      console.error('添加商品失败:', error)
      if (error.response?.status === 401) {
        ElMessage.error('登录已过期，正在跳转到登录页面...')
        userStore.logout(true) // 自动跳转
        return false
      }
      ElMessage.error('添加失败')
      return false
    }
  }

  // 更新商品
  const updateProduct = async (productId, productData) => {
    const userStore = useUserStore()
    if (!userStore.token) {
      ElMessage.error('请先登录')
      return false
    }

    try {
      // 确保数据格式正确
      const formattedData = {
        ...productData,
        imagePath: productData.imgUrl || productData.imagePath // 确保imagePath字段存在
      };
      
      console.log('更新商品数据:', formattedData);
      
      const response = await productUpdateService(productId, formattedData)
      if (response.code === 0) {
        ElMessage.success('更新成功')
        await getProductList() // 刷新商品列表
        return true
      } else {
        ElMessage.error(response.message || '更新失败')
        return false
      }
    } catch (error) {
      console.error('更新商品失败:', error)
      if (error.response?.status === 401) {
        ElMessage.error('登录已过期，正在跳转到登录页面...')
        userStore.logout(true) // 自动跳转
        return false
      }
      ElMessage.error('更新失败')
      return false
    }
  }

  // 删除商品
  const deleteProduct = async (productId) => {
    const userStore = useUserStore()
    if (!userStore.token) {
      ElMessage.error('请先登录')
      return false
    }

    try {
      const response = await productDeleteService(productId)
      if (response.code === 0) {
        ElMessage.success('删除成功')
        await getProductList() // 刷新商品列表
        return true
      } else {
        ElMessage.error(response.message || '删除失败')
        return false
      }
    } catch (error) {
      console.error('删除商品失败:', error)
      if (error.response?.status === 401) {
        ElMessage.error('登录已过期，正在跳转到登录页面...')
        userStore.logout(true) // 自动跳转
        return false
      }
      ElMessage.error('删除失败')
      return false
    }
  }

  return {
    products,
    getProductList,
    getProductDetail,
    addProduct,
    updateProduct,
    deleteProduct
  }
}, {
  persist: {
    key: 'product-store',
    storage: localStorage,
    paths: ['products']  // 只持久化 products
  }
})
