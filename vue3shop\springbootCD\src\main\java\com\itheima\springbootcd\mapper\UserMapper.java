package com.itheima.springbootcd.mapper;

import com.itheima.springbootcd.pojo.User;
import org.apache.ibatis.annotations.*;

@Mapper
public interface UserMapper {
    @Select("select * from user where username=#{username}")
    User findByUsername(String username);
    @Insert("insert into user(username,password,register_time,status,role)" +
            " values(#{username},#{password},now(),0,#{role})")
    void add(String username, String password, String role);
    @Update("update user set nickname=#{nickname},email=#{email},phone=#{phone},address=#{address} where user_id=#{userId}")
    void update(User user);
    @Update("update user set avatar=#{avatarUrl} where user_id=#{id}")
    void updateAvatar(String avatarUrl, Integer id);
    @Delete("DELETE FROM user WHERE user_id = #{userId}")
    void deleteById(Integer userId);
    @Select("select * from user where user_id=#{userId}")
    User findById(Integer userId);
    @Update("update user set password=#{md5String} where user_id=#{id}")
    void updatePwd(String md5String, Integer id);
}
