<template>
  <div class="permission-test">
    <RtHeader title="权限系统测试" />
    
    <div class="container">
      <el-card class="user-info-card">
        <template #header>
          <div class="card-header">
            <span>当前用户信息</span>
          </div>
        </template>
        
        <div v-if="userStore.userInfo" class="user-info">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="用户名">
              {{ userStore.userInfo.username }}
            </el-descriptions-item>
            <el-descriptions-item label="用户角色">
              <UserRoleBadge :role="userStore.userInfo.role" />
            </el-descriptions-item>
            <el-descriptions-item label="邮箱">
              {{ userStore.userInfo.email || '未设置' }}
            </el-descriptions-item>
            <el-descriptions-item label="电话">
              {{ userStore.userInfo.phone || '未设置' }}
            </el-descriptions-item>
            <el-descriptions-item label="昵称">
              {{ userStore.userInfo.nickname || '未设置' }}
            </el-descriptions-item>
            <el-descriptions-item label="注册时间">
              {{ formatDate(userStore.userInfo.registerTime) }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
        
        <div v-else class="no-user">
          <el-empty description="请先登录" />
        </div>
      </el-card>
      
      <el-card class="permission-test-card">
        <template #header>
          <div class="card-header">
            <span>权限功能测试</span>
          </div>
        </template>
        
        <div class="test-buttons">
          <!-- 普通用户功能 -->
          <div class="test-section">
            <h3>普通用户功能（所有用户可用）</h3>
            <el-space wrap>
              <el-button @click="testViewProducts">浏览商品</el-button>
              <el-button @click="testViewManufacturers">查看生产商</el-button>
              <el-button @click="testAddToCart">添加到购物车</el-button>
            </el-space>
          </div>
          
          <!-- 生产商功能 -->
          <div class="test-section">
            <h3>生产商功能（需要生产商权限）</h3>
            <el-space wrap>
              <el-button 
                type="warning" 
                @click="testAddProduct"
                :disabled="!isManufacturer && !isAdmin"
              >
                添加商品
              </el-button>
              <el-button 
                type="warning" 
                @click="testUpdateProduct"
                :disabled="!isManufacturer && !isAdmin"
              >
                更新商品
              </el-button>
              <el-button 
                type="warning" 
                @click="testManageProducts"
                :disabled="!isManufacturer && !isAdmin"
              >
                管理商品
              </el-button>
            </el-space>
          </div>
          
          <!-- 管理员功能 -->
          <div class="test-section">
            <h3>管理员功能（需要管理员权限）</h3>
            <el-space wrap>
              <el-button 
                type="danger" 
                @click="testAddManufacturer"
                :disabled="!isAdmin"
              >
                添加生产商
              </el-button>
              <el-button 
                type="danger" 
                @click="testDeleteManufacturer"
                :disabled="!isAdmin"
              >
                删除生产商
              </el-button>
              <el-button 
                type="danger" 
                @click="testManageUsers"
                :disabled="!isAdmin"
              >
                用户管理
              </el-button>
            </el-space>
          </div>
        </div>
      </el-card>
      
      <el-card class="role-switch-card">
        <template #header>
          <div class="card-header">
            <span>角色切换测试</span>
          </div>
        </template>
        
        <div class="role-switch">
          <p>测试不同角色的账号：</p>
          <el-space wrap>
            <el-button @click="switchToCustomer">切换到普通用户</el-button>
            <el-button @click="switchToManufacturer">切换到生产商</el-button>
            <el-button @click="switchToAdmin">切换到管理员</el-button>
          </el-space>
          
          <el-alert
            title="测试账号信息"
            type="info"
            :closable="false"
            style="margin-top: 16px;"
          >
            <p>普通用户: user001 / 123456</p>
            <p>生产商: asus_manager / 123456</p>
            <p>管理员: admin / 123456</p>
          </el-alert>
        </div>
      </el-card>
    </div>
    
    <RtTabBar />
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import UserRoleBadge from '@/components/UserRoleBadge.vue'

const router = useRouter()
const userStore = useUserStore()

// 计算用户角色
const isCustomer = computed(() => userStore.userInfo?.role === 'CUSTOMER')
const isManufacturer = computed(() => userStore.userInfo?.role === 'MANUFACTURER')
const isAdmin = computed(() => userStore.userInfo?.role === 'ADMIN')

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '未知'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 测试功能
const testViewProducts = () => {
  ElMessage.success('浏览商品功能 - 所有用户都可以使用')
  router.push('/category')
}

const testViewManufacturers = () => {
  ElMessage.success('查看生产商功能 - 所有用户都可以使用')
  router.push('/manufacturer')
}

const testAddToCart = () => {
  ElMessage.success('添加到购物车功能 - 所有用户都可以使用')
  router.push('/cart')
}

const testAddProduct = () => {
  if (!isManufacturer.value && !isAdmin.value) {
    ElMessage.error('权限不足：只有生产商和管理员可以添加商品')
    return
  }
  ElMessage.success('添加商品功能 - 生产商和管理员可用')
}

const testUpdateProduct = () => {
  if (!isManufacturer.value && !isAdmin.value) {
    ElMessage.error('权限不足：只有生产商和管理员可以更新商品')
    return
  }
  ElMessage.success('更新商品功能 - 生产商和管理员可用')
}

const testManageProducts = () => {
  if (!isManufacturer.value && !isAdmin.value) {
    ElMessage.error('权限不足：只有生产商和管理员可以管理商品')
    return
  }
  ElMessage.success('管理商品功能 - 生产商和管理员可用')
  router.push('/product/manage')
}

const testAddManufacturer = () => {
  if (!isAdmin.value) {
    ElMessage.error('权限不足：只有管理员可以添加生产商')
    return
  }
  ElMessage.success('添加生产商功能 - 仅管理员可用')
}

const testDeleteManufacturer = () => {
  if (!isAdmin.value) {
    ElMessage.error('权限不足：只有管理员可以删除生产商')
    return
  }
  ElMessage.success('删除生产商功能 - 仅管理员可用')
}

const testManageUsers = () => {
  if (!isAdmin.value) {
    ElMessage.error('权限不足：只有管理员可以管理用户')
    return
  }
  ElMessage.success('用户管理功能 - 仅管理员可用')
}

// 角色切换（实际上是跳转到登录页）
const switchToCustomer = () => {
  ElMessage.info('请使用普通用户账号登录：user001 / 123456')
  router.push('/login')
}

const switchToManufacturer = () => {
  ElMessage.info('请使用生产商账号登录：asus_manager / 123456')
  router.push('/login')
}

const switchToAdmin = () => {
  ElMessage.info('请使用管理员账号登录：admin / 123456')
  router.push('/login')
}
</script>

<style scoped>
.permission-test {
  min-height: 100vh;
  background: #f6f8fa;
}

.container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  margin-bottom: 80px;
}

.user-info-card,
.permission-test-card,
.role-switch-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.no-user {
  text-align: center;
  padding: 40px 0;
}

.test-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
}

.test-section h3 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
}

.role-switch p {
  margin: 0 0 12px 0;
  color: #606266;
}

@media (max-width: 768px) {
  .container {
    padding: 16px;
  }
  
  .test-section {
    padding: 12px;
  }
}
</style>
