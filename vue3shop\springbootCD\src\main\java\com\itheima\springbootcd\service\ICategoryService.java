package com.itheima.springbootcd.service;

import com.itheima.springbootcd.pojo.Category;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
public interface ICategoryService extends IService<Category> {

    void add(Category category);
    List<Category> list();
    List<Category> listAll();

    Category findById(Integer id);

    void update(Category category);
    void delete(Integer categoryId);

    List<Category> search(String categoryName);
}
