package com.itheima.springbootcd.service;

import com.itheima.springbootcd.pojo.Manufacturer;

import java.util.List;

/**
 * 生产商Service接口
 */
public interface ManufacturerService {

    /**
     * 获取所有生产商列表
     */
    List<Manufacturer> list();

    /**
     * 根据ID查询生产商详情
     */
    Manufacturer findById(Integer id);

    /**
     * 添加生产商
     */
    void add(Manufacturer manufacturer);

    /**
     * 更新生产商信息
     */
    void update(Manufacturer manufacturer);

    /**
     * 删除生产商
     */
    void deleteById(Integer id);

    /**
     * 根据用户ID查询生产商信息
     */
    Manufacturer findByUserId(Integer userId);
}
