package com.itheima.springbootcd.service.impl;

import com.itheima.springbootcd.mapper.UserMapper;
import com.itheima.springbootcd.pojo.User;
import com.itheima.springbootcd.service.UserService;
import com.itheima.springbootcd.utils.Md5Util;
import com.itheima.springbootcd.utils.RedisUtil;
import com.itheima.springbootcd.utils.ThreadLocalUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Random;

@Service
public class UserServiceImpl implements UserService {
    @Autowired
    private UserMapper userMapper;

    @Autowired
    private RedisUtil redisUtil;

    private static final String USER_CACHE_PREFIX = "user:";

    @Override
    public User findByUsername(String username) {
        System.out.println("Finding user by username: " + username);

        String cacheKey = USER_CACHE_PREFIX + username;
        User user = (User) redisUtil.get(cacheKey);

        if (user != null) {
            System.out.println("User found in cache: " + username);
            if (user.getPassword() == null) {
                System.out.println("Cached user data is incomplete, fetching from database");
                redisUtil.del(cacheKey);
                user = null;
            } else {
                return user;
            }
        }

        System.out.println("User not found in cache, checking database: " + username);

        synchronized (this) {
            user = (User) redisUtil.get(cacheKey);
            if (user != null && user.getPassword() != null) {
                System.out.println("User found in cache after double-check: " + username);
                return user;
            }

            user = userMapper.findByUsername(username);

            if (user != null) {
                System.out.println("User found in database: " + username);
                long expireTime = 1800 + new Random().nextInt(600);
                redisUtil.set(cacheKey, user, expireTime);
            } else {
                System.out.println("User not found in database: " + username);
            }
            return user;
        }
    }

    @Override
    public void register(String username, String password) {
        userMapper.add(username, Md5Util.getMD5String(password), "CUSTOMER");
    }

    @Override
    public void register(String username, String password, String role) {
        userMapper.add(username, Md5Util.getMD5String(password), role);
    }

    @Override
    public void update(User user) {
        userMapper.update(user);
        String cacheKey = USER_CACHE_PREFIX + user.getUsername();
        redisUtil.del(cacheKey);
    }

    @Override
    public void updateAvatar(String avatarUrl) {
        Map<String, Object> map = ThreadLocalUtil.get();
        Integer userId = (Integer) map.get("id");
        String username = (String) map.get("username");
        userMapper.updateAvatar(avatarUrl, userId);
        String cacheKey = USER_CACHE_PREFIX + username;
        redisUtil.del(cacheKey);
    }

    @Override
    public void deleteById(Integer userId) {
        User user = userMapper.findById(userId);
        userMapper.deleteById(userId);
        if (user != null) {
            String cacheKey = USER_CACHE_PREFIX + user.getUsername();
            redisUtil.del(cacheKey);
        }
    }

    @Override
    public void updatePwd(String newPwd) {
        Map<String, Object> map = ThreadLocalUtil.get();
        Integer id = (Integer) map.get("id");
        String username = (String) map.get("username");
        userMapper.updatePwd(Md5Util.getMD5String(newPwd), id);
        String cacheKey = USER_CACHE_PREFIX + username;
        redisUtil.del(cacheKey);
    }
}

